import React from 'react';
import { Link } from 'react-router-dom';
import { Code } from 'lucide-react';

const Footer = () => {
  const linkSections = [
    {
      title: 'Navigate',
      links: [
        { path: '/', label: 'Home' },
        { path: '/scripts', label: 'Scrip<PERSON>' },
        { path: '/request-script', label: 'Request Script' },
      ]
    },
    {
      title: 'Resources',
      links: [
        { path: '/faq', label: 'FAQ' },
        { path: '/terms', label: 'Terms of Service' },
        { path: '/privacy', label: 'Privacy Policy' },
        { path: '/disclaimer', label: 'Disclaimer' },
      ]
    },
    {
      title: 'Connect',
      links: [
        { path: '/contact', label: 'Contact Us' },
        { path: '/discord', label: 'Discord' },
      ]
    }
  ];

  return (
    <footer className="bg-background/50 border-t mt-16 backdrop-blur-lg">
      <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
        <div className="xl:grid xl:grid-cols-3 xl:gap-8">
          <div className="space-y-4">
            <Link to="/" className="flex items-center space-x-2">
              <div className="w-10 h-10 bg-primary rounded-lg flex items-center justify-center shadow-lg shadow-primary/30">
                <Code className="w-6 h-6 text-primary-foreground" />
              </div>
              <span className="text-xl font-bold text-foreground">6FootScripts</span>
            </Link>
            <p className="text-muted-foreground text-sm max-w-xs">
              High-quality, reliable scripts to elevate your gaming experience.
            </p>
          </div>
          <div className="mt-12 grid grid-cols-2 gap-8 xl:mt-0 xl:col-span-2">
            <div className="md:grid md:grid-cols-3 md:gap-8 col-span-2">
              {linkSections.map((section) => (
                <div key={section.title}>
                  <p className="font-semibold text-foreground tracking-wider uppercase">{section.title}</p>
                  <ul className="mt-4 space-y-3">
                    {section.links.map((link) => {
                      return (
                        <li key={link.label}>
                          <Link to={link.path} className="text-muted-foreground hover:text-primary transition-colors flex items-center">
                            {link.label === 'Discord' ? (
                               <img src="https://storage.googleapis.com/hostinger-horizons-assets-prod/b372f44b-c5a5-4e91-95b3-5f8a56c2e92a/04746783c68f91f53e17310747e1c8db.png" alt="Discord Logo" className="w-5 h-5 mr-2" />
                            ) : null}
                            {link.label}
                          </Link>
                        </li>
                      );
                    })}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>
        <div className="mt-12 border-t border-border/50 pt-8 flex flex-col sm:flex-row justify-between items-center gap-4">
          <p className="text-muted-foreground text-sm text-center sm:text-left">
            &copy; {new Date().getFullYear()} 6FootScripts. All Rights Reserved.
          </p>
          <p className="text-muted-foreground text-sm text-center sm:text-right">
            Not affiliated with Roblox Corporation or any other game publisher.
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;