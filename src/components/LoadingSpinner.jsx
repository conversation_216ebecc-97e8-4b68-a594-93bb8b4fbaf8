import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

const loadingMessages = [
  "Compiling epic scripts...",
  "Polishing the pixels...",
  "Waking up the hamsters...",
  "Reticulating splines...",
  "Aligning the flux capacitor...",
  "Charging the lasers...",
  "Doing the robot...",
  "Counting to infinity (almost)...",
];

const LoadingSpinner = () => {
  const [messageIndex, setMessageIndex] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setMessageIndex((prevIndex) => (prevIndex + 1) % loadingMessages.length);
    }, 2500);
    return () => clearInterval(interval);
  }, []);

  const devImages = [
    'https://storage.googleapis.com/hostinger-horizons-assets-prod/b372f44b-c5a5-4e91-95b3-5f8a56c2e92a/98f0f8020b8a290f454abc12077ff510.png',
    'https://storage.googleapis.com/hostinger-horizons-assets-prod/b372f44b-c5a5-4e91-95b3-5f8a56c2e92a/b37f650846d4e2af8503a5017ed63c94.png'
  ];

  return (
    <div className="flex flex-col items-center justify-center gap-6">
      <div className="relative w-24 h-24">
        {devImages.map((src, index) => (
          <motion.img
            key={src}
            src={src}
            alt={`Developer ${index + 1} avatar`}
            className="absolute top-0 left-0 w-full h-full object-cover rounded-full shadow-lg"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{
                opacity: [0, 1, 1, 0, 0],
                scale: [0.8, 1, 1, 0.8, 0.8],
            }}
            transition={{
                duration: 5,
                repeat: Infinity,
                delay: index * 2.5,
                ease: "easeInOut"
            }}
          />
        ))}
        <motion.div
          className="absolute inset-0 border-4 border-primary/50 rounded-full"
          animate={{ rotate: 360 }}
          transition={{ duration: 1.5, repeat: Infinity, ease: "linear" }}
        />
      </div>
      <AnimatePresence mode="wait">
        <motion.p
          key={messageIndex}
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          transition={{ duration: 0.5 }}
          className="text-muted-foreground font-medium tracking-wider text-sm"
        >
          {loadingMessages[messageIndex]}
        </motion.p>
      </AnimatePresence>
    </div>
  );
};

export default LoadingSpinner;