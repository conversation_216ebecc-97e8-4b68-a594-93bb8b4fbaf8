import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Menu, X, Code, Home, Mail, Sun, Moon, FilePlus, Shield } from 'lucide-react';
import { useTheme } from '@/contexts/ThemeProvider';
import { useAdminAuth } from '@/contexts/AdminAuthContext';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';

const Navigation = () => {
  const [isOpen, setIsOpen] = useState(false);
  const location = useLocation();
  const { theme, setTheme } = useTheme();
  const { isAdmin } = useAdminAuth();

  const navItems = [
    { path: '/', label: 'Home', icon: Home },
    { path: '/scripts', label: 'Scripts', icon: Code },
    { path: '/contact', label: 'Contact Us', icon: Mail },
  ];

  const isActive = (path) => location.pathname === path;

  const toggleTheme = () => {
    setTheme(theme === 'dark' ? 'light' : 'dark');
  };

  return (
    <nav className="sticky top-0 z-50 backdrop-blur-lg bg-background/70 border-b border-border/50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-20">
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            className="flex items-center space-x-2"
          >
            <div className="flex items-center space-x-2" >
              <div 
                className="w-10 h-10 bg-primary rounded-lg flex items-center justify-center shadow-lg shadow-primary/30"
              >
                <span className="text-2xl font-bold text-primary-foreground select-none">6</span>
              </div>
              <Link to="/">
                <span className="text-xl font-bold text-foreground">FootScripts</span>
              </Link>
            </div>
          </motion.div>

          <div className="hidden md:flex items-center space-x-1">
            {navItems.map((item, index) => {
              const Icon = item.icon;
              return (
                <motion.div
                  key={item.path}
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <Button asChild variant="ghost" className={`text-muted-foreground hover:text-foreground ${isActive(item.path) ? 'text-primary' : ''}`}>
                    <Link to={item.path}>
                      <Icon className="w-4 h-4 mr-2" />
                      {item.label}
                    </Link>
                  </Button>
                </motion.div>
              );
            })}
          </div>

          <div className="flex items-center gap-4">
            <div className="hidden md:flex items-center gap-4">
              {isAdmin && (
                <Button asChild variant="outline">
                  <Link to="/admin/login">
                    <Shield className="w-4 h-4 mr-2" />
                    Admin Panel
                  </Link>
                </Button>
              )}
              <Button asChild>
                <Link to="/request-script">
                  <FilePlus className="w-4 h-4 mr-2" />
                  Request Script
                </Link>
              </Button>
            </div>
            <div className="flex items-center space-x-2">
              <Sun className="h-5 w-5 text-muted-foreground transition-all scale-100 dark:scale-0" />
              <Switch
                id="theme-switch-desktop"
                checked={theme === 'dark'}
                onCheckedChange={toggleTheme}
              />
              <Moon className="h-5 w-5 text-muted-foreground transition-all scale-0 dark:scale-100" />
              <Label htmlFor="theme-switch-desktop" className="sr-only">Toggle theme</Label>
            </div>
            <div className="md:hidden">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setIsOpen(!isOpen)}
              >
                {isOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
              </Button>
            </div>
          </div>
        </div>
      </div>

      {isOpen && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          className="md:hidden bg-background/95 backdrop-blur-md border-t border-border"
        >
          <div className="px-4 py-4 space-y-2">
            {navItems.map((item) => {
              const Icon = item.icon;
              return (
                <Link
                  key={item.path}
                  to={item.path}
                  onClick={() => setIsOpen(false)}
                  className={`flex items-center space-x-3 px-4 py-3 rounded-lg transition-colors duration-300 ${
                    isActive(item.path)
                      ? 'bg-primary/10 text-primary'
                      : 'text-muted-foreground hover:text-foreground'
                  }`}
                >
                  <Icon className="w-5 h-5" />
                  <span className="font-medium">{item.label}</span>
                </Link>
              );
            })}
             <Link
                to="/request-script"
                onClick={() => setIsOpen(false)}
                className={`flex items-center space-x-3 px-4 py-3 rounded-lg transition-colors duration-300 text-muted-foreground hover:text-foreground`}
              >
                <FilePlus className="w-5 h-5" />
                <span className="font-medium">Request Script</span>
              </Link>
              {isAdmin && (
                <Link
                  to="/admin/login"
                  onClick={() => setIsOpen(false)}
                  className={`flex items-center space-x-3 px-4 py-3 rounded-lg transition-colors duration-300 bg-primary/10 text-primary`}
                >
                  <Shield className="w-5 h-5" />
                  <span className="font-medium">Admin Panel</span>
                </Link>
              )}
          </div>
        </motion.div>
      )}
    </nav>
  );
};

export default Navigation;