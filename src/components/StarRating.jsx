import React, { useState } from 'react';
import { Star } from 'lucide-react';
import { cn } from '@/lib/utils';

const StarRating = ({ totalStars = 5, rating = 0, onRate, size = 4, readOnly = false }) => {
  const [hoverRating, setHoverRating] = useState(0);

  const handleMouseOver = (rating) => {
    if (readOnly) return;
    setHoverRating(rating);
  };

  const handleMouseLeave = () => {
    if (readOnly) return;
    setHoverRating(0);
  };

  const handleClick = (rating) => {
    if (readOnly) return;
    if (onRate) {
      onRate(rating);
    }
  };

  return (
    <div className="flex items-center space-x-1">
      {[...Array(totalStars)].map((_, index) => {
        const ratingValue = index + 1;
        const isFilled = ratingValue <= (hoverRating || rating);
        
        return (
          <Star
            key={index}
            className={cn(
              `w-${size} h-${size} transition-colors duration-200`,
              isFilled ? 'text-yellow-400 fill-yellow-400' : 'text-gray-300 dark:text-gray-600',
              !readOnly && 'cursor-pointer'
            )}
            onMouseOver={() => handleMouseOver(ratingValue)}
            onMouseLeave={handleMouseLeave}
            onClick={() => handleClick(ratingValue)}
          />
        );
      })}
    </div>
  );
};

export default StarRating;