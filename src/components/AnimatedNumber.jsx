import React, { useEffect, useRef } from 'react';
import { animate, useInView } from 'framer-motion';

const AnimatedNumber = ({ value }) => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true });

  useEffect(() => {
    if (isInView) {
      const node = ref.current;
      if (node) {
        const controls = animate(0, value, {
          duration: 2,
          onUpdate(latest) {
            node.textContent = Math.round(latest).toLocaleString();
          },
        });
        return () => controls.stop();
      }
    }
  }, [isInView, value]);

  return <span ref={ref}>0</span>;
};

export default AnimatedNumber;