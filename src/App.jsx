
import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AnimatePresence } from 'framer-motion';
import { Toaster } from '@/components/ui/toaster';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import Home from '@/pages/Home';
import Scripts from '@/pages/Scripts';
import Contact from '@/pages/Contact';
import FAQ from '@/pages/FAQ';
import Terms from '@/pages/Terms';
import Privacy from '@/pages/Privacy';
import Disclaimer from '@/pages/Disclaimer';
import RequestScript from '@/pages/RequestScript';
import DiscordRedirect from '@/pages/DiscordRedirect';
import GlobalLoader from '@/components/GlobalLoader';
import NotFound from '@/pages/NotFound';
import AdminLogin from '@/pages/admin/AdminLogin';
import ProtectedRoute from '@/components/ProtectedRoute';
import ErrorBoundary from '@/components/ErrorBoundary';
import { useUserTracker } from '@/hooks/useUserTracker';
import Banned from '@/pages/Banned';
import AdminLayout from '@/pages/admin/layout/AdminLayout';
import AdminMainPanel from '@/pages/admin/AdminMainPanel';
import AdminKeys from '@/pages/admin/AdminKeys';
import AdminRequests from '@/pages/admin/AdminRequests';
import AdminScripts from '@/pages/admin/AdminScripts';
import AdminUsers from '@/pages/admin/AdminUsers';
import GetHwid from '@/pages/GetHwid';
import AdminAccessGuard from '@/components/AdminAccessGuard';

const SiteLayout = ({ children }) => {
  return (
    <div className="min-h-screen flex flex-col main-bg">
      <Navigation />
      <main className="flex-grow">
        {children}
      </main>
      <Footer />
    </div>
  );
};

function AppContent() {
  const { isBlacklisted, loading: userTrackerLoading } = useUserTracker();
  const [minTimePassed, setMinTimePassed] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      setMinTimePassed(true);
    }, 2500);

    return () => clearTimeout(timer);
  }, []);

  if (userTrackerLoading || !minTimePassed) {
    return <GlobalLoader />;
  }

  if (isBlacklisted) {
    return <Banned />;
  }

  return (
    <AnimatePresence mode="wait">
      <Routes>
        <Route path="/admin/*" element={
          <AdminAccessGuard>
            <Routes>
              <Route path="login" element={<AdminLogin />} />
              <Route element={<ProtectedRoute><AdminLayout /></ProtectedRoute>}>
                <Route path="main_panel" element={<AdminMainPanel />} />
                <Route path="keys" element={<AdminKeys />} />
                <Route path="requests" element={<AdminRequests />} />
                <Route path="scripts" element={<AdminScripts />} />
                <Route path="users" element={<AdminUsers />} />
                <Route path="/" element={<Navigate to="/admin/main_panel" replace />} />
              </Route>
            </Routes>
          </AdminAccessGuard>
        } />

        <Route path="/*" element={
          <SiteLayout>
            <Routes>
              <Route path="/" element={<Home />} />
              <Route path="/scripts" element={<Scripts />} />
              <Route path="/request-script" element={<RequestScript />} />
              <Route path="/contact" element={<Contact />} />
              <Route path="/faq" element={<FAQ />} />
              <Route path="/terms" element={<Terms />} />
              <Route path="/privacy" element={<Privacy />} />
              <Route path="/disclaimer" element={<Disclaimer />} />
              <Route path="/discord" element={<DiscordRedirect />} />
              <Route path="/get-hwid" element={<GetHwid />} />
              <Route path="*" element={<NotFound />} />
            </Routes>
          </SiteLayout>
        }/>
      </Routes>
    </AnimatePresence>
  );
}

function App() {
  return (
    <ErrorBoundary>
      <Router>
        <AppContent />
        <Toaster />
      </Router>
    </ErrorBoundary>
  );
}

export default App;
