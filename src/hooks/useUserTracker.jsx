import { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { apiClient } from '@/lib/apiClient';
import { getHwid } from '@/lib/hwid';

export const useUserTracker = () => {
    const [isBlacklisted, setIsBlacklisted] = useState(false);
    const [loading, setLoading] = useState(true);
    const location = useLocation();

    useEffect(() => {
        const trackUser = async () => {
            setLoading(true);
            const hwid = getHwid();
            if (!hwid) {
                setLoading(false);
                return;
            }

            try {
                // Check user status using secure API
                const statusResult = await apiClient.checkUserStatus(hwid);
                setIsBlacklisted(statusResult.isBlacklisted);

                // Track user visit if not on admin pages
                if (!location.pathname.startsWith('/admin')) {
                    await apiClient.trackUser(hwid, location.pathname);
                }
            } catch (error) {
                console.error("An error occurred during user tracking:", error);
            } finally {
                setLoading(false);
            }
        };

        trackUser();
    }, [location.pathname]);

    return { isBlacklisted, loading };
};