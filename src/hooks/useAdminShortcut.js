import { useEffect, useState, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';

export const useAdminShortcut = (targetSequence) => {
  const navigate = useNavigate();
  const [keys, setKeys] = useState([]);

  const keydownHandler = useCallback((e) => {
    setKeys((prevKeys) => {
      const newKeys = [...prevKeys, e.key.toLowerCase()];
      if (newKeys.length > targetSequence.length) {
        newKeys.shift();
      }
      if (JSON.stringify(newKeys) === JSON.stringify(targetSequence)) {
        navigate('/admin/login');
        return [];
      }
      return newKeys;
    });
  }, [navigate, targetSequence]);

  useEffect(() => {
    window.addEventListener('keydown', keydownHandler);
    return () => {
      window.removeEventListener('keydown', keydownHandler);
    };
  }, [keydownHandler]);
};