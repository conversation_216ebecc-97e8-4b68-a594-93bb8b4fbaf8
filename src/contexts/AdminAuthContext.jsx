
import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { getHwid } from '@/lib/hwid';

const AdminAuthContext = createContext({
  isAdmin: false,
  adminName: null,
  loading: true,
  getAuthHeaders: () => ({}),
});

export const useAdminAuth = () => useContext(AdminAuthContext);

export const AdminAuthProvider = ({ children }) => {
  const [isAdmin, setIsAdmin] = useState(false);
  const [adminName, setAdminName] = useState(null);
  const [loading, setLoading] = useState(true);

  const checkAdminStatus = useCallback(() => {
    setLoading(true);
    const isAuthenticated = sessionStorage.getItem('6footscripts_admin_auth') === 'true';
    const name = sessionStorage.getItem('6footscripts_admin_name');
    
    setIsAdmin(isAuthenticated);
    setAdminName(name);
    setLoading(false);
  }, []);

  useEffect(() => {
    checkAdminStatus();
    
    const handleStorageChange = (e) => {
      if (e.key === '6footscripts_admin_auth' || e.key === '6footscripts_admin_name') {
        checkAdminStatus();
      }
    };
    
    window.addEventListener('storage', handleStorageChange);
    
    return () => {
      window.removeEventListener('storage', handleStorageChange);
    }
  }, [checkAdminStatus]);

  const getAuthHeaders = () => {
    const accessKey = sessionStorage.getItem('6footscripts_admin_key');
    const hwid = getHwid();
    return { accessKey, hwid };
  };

  const value = { isAdmin, adminName, loading, getAuthHeaders };

  return (
    <AdminAuthContext.Provider value={value}>
      {children}
    </AdminAuthContext.Provider>
  );
};
