import React from 'react';
import { Helmet } from 'react-helmet';
import { motion } from 'framer-motion';
import { MessageCircle, Users, ChevronRight, HelpCircle, Briefcase } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import PageWrapper from '@/components/PageWrapper';
import { Link } from 'react-router-dom';

const Contact = () => {
  const developers = [
    {
      name: '6Foot4Honda',
      title: 'Script Owner & Developer',
      contactLink: 'https://discord.com/users/240070307094724608',
      avatar: 'https://storage.googleapis.com/hostinger-horizons-assets-prod/b372f44b-c5a5-4e91-95b3-5f8a56c2e92a/98f0f8020b8a290f454abc12077ff510.png',
      alt: 'Avatar for 6Foot4Honda'
    },
    {
      name: 'Luna<PERSON><PERSON>',
      title: 'Script Developer & Website Owner',
      contactLink: 'https://discord.com/users/1012750340971581561',
      avatar: 'https://storage.googleapis.com/hostinger-horizons-assets-prod/b372f44b-c5a5-4e91-95b3-5f8a56c2e92a/b37f650846d4e2af8503a5017ed63c94.png',
      alt: 'Avatar for Lunarbine'
    }
  ];

  const contactOptions = [
    {
      icon: Users,
      title: 'Join our Community',
      description: 'For general support, script help, and announcements.',
      buttonText: 'Join Discord Server',
      link: '/discord'
    },
    {
      icon: HelpCircle,
      title: 'Frequently Asked Questions',
      description: 'Find answers to common questions about our scripts and services.',
      buttonText: 'Visit FAQ',
      link: '/faq'
    },
  ];

  return (
    <>
      <Helmet>
        <title>Contact Us - 6FootScripts</title>
        <meta name="description" content="Get in touch with the 6FootScripts team. We're here to help with your questions, support needs, and project inquiries." />
      </Helmet>

      <PageWrapper
        title={<>Get in <span className="bg-gradient-to-r from-primary to-pink-500 bg-clip-text text-transparent">Touch</span></>}
        description="We're here to help. Find the best way to reach us below."
      >
        <div className="max-w-4xl mx-auto">
          <div className="grid md:grid-cols-2 gap-8 mb-12">
            {contactOptions.map((option, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
              >
                <Card className="h-full flex flex-col text-center p-6">
                  <option.icon className="w-12 h-12 text-primary mx-auto mb-4" />
                  <CardHeader className="p-0">
                    <CardTitle>{option.title}</CardTitle>
                    <CardDescription className="mt-2">{option.description}</CardDescription>
                  </CardHeader>
                  <CardContent className="p-0 mt-6 flex-grow flex items-end justify-center">
                    <Button asChild size="lg" className="w-full">
                      <Link to={option.link}>
                        {option.buttonText} <ChevronRight className="w-4 h-4 ml-2" />
                      </Link>
                    </Button>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>

          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold tracking-tight">Direct Contact</h2>
            <p className="text-muted-foreground mt-2">For specific inquiries, you can reach out to our developers directly.</p>
          </div>

          <div className="grid md:grid-cols-2 gap-8">
            {developers.map((dev, index) => (
              <motion.div
                key={dev.name}
                initial={{ opacity: 0, y: 50 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.2 + index * 0.2 }}
              >
                <Card className="h-full flex flex-col items-center p-8 text-center">
                  <img src={dev.avatar} alt={dev.alt} className="w-24 h-24 rounded-full mb-4 border-4 border-border" />
                  <CardHeader className="p-0">
                    <CardTitle>{dev.name}</CardTitle>
                    <CardDescription>{dev.title}</CardDescription>
                  </CardHeader>
                  <CardContent className="p-0 mt-6 flex-grow flex items-end">
                    <Button asChild className="w-full" variant="outline">
                      <a href={dev.contactLink} target="_blank" rel="noopener noreferrer">
                        <MessageCircle className="w-5 h-5 mr-2" />
                        Contact on Discord
                      </a>
                    </Button>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </PageWrapper>
    </>
  );
};

export default Contact;