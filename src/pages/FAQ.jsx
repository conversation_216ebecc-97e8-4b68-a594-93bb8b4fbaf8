import React from 'react';
import { Helmet } from 'react-helmet';
import { motion } from 'framer-motion';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import PageWrapper from '@/components/PageWrapper';

const faqData = [
  {
    question: "What are 6FootScripts?",
    answer: "6FootScripts is a collection of high-quality, reliable scripts designed to enhance your gaming experience in various popular online games. Our scripts are developed with performance and user-friendliness in mind."
  },
  {
    question: "Are the scripts safe to use?",
    answer: "We prioritize safety and security in our development process. However, the use of any third-party scripts can carry risks. We recommend using them responsibly and being aware of the terms of service for any game you play. 6FootScripts is not responsible for any account actions taken against you for using our scripts."
  },
  {
    question: "How do I use a script?",
    answer: "Each script comes with instructions. Generally, you will need a script executor compatible with the game. You can copy the script code from our 'Scripts' page and paste it into your executor."
  },
  {
    question: "Are the scripts free?",
    answer: "Yes, all scripts listed on our website are currently free to use. We believe in providing value to the gaming community."
  },
  {
    question: "How often are scripts updated?",
    answer: "We strive to keep our scripts updated to be compatible with the latest game versions. You can check the 'Last Updated' date and the 'Update Log' for each script on the Scripts page."
  },
  {
    question: "What does 'Discontinued' status mean?",
    answer: "A 'Discontinued' script is no longer being actively developed or supported. It may not work with current versions of the game and is provided as-is for archival purposes."
  },
  {
    question: "Who can I contact for support?",
    answer: "For general questions, you can use our Contact page. For script-specific issues, we recommend joining our community Discord where you can get help from us and other users."
  }
];

const FAQ = () => {
  return (
    <>
      <Helmet>
        <title>FAQ - 6FootScripts</title>
        <meta name="description" content="Find answers to frequently asked questions about 6FootScripts, our scripts, and how to use them." />
      </Helmet>
      <PageWrapper
        title={<>Frequently Asked <span className="bg-gradient-to-r from-primary to-pink-500 bg-clip-text text-transparent">Questions</span></>}
        description="Have a question? We've got answers. If you can't find what you're looking for, feel free to contact us."
      >
        <Accordion type="single" collapsible className="w-full bg-card border rounded-lg p-4">
          {faqData.map((item, index) => (
            <AccordionItem value={`item-${index}`} key={index} className={index === faqData.length - 1 ? 'border-b-0' : ''}>
              <AccordionTrigger className="text-left hover:no-underline text-lg">{item.question}</AccordionTrigger>
              <AccordionContent className="text-muted-foreground">
                {item.answer}
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
      </PageWrapper>
    </>
  );
};

export default FAQ;