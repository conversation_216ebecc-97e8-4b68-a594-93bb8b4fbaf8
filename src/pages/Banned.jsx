import React from 'react';
import { Helmet } from 'react-helmet';
import { motion } from 'framer-motion';
import { ShieldOff } from 'lucide-react';

const Banned = () => {
  return (
    <>
      <Helmet>
        <title>Access Denied - 6FootScripts</title>
      </Helmet>
      <div className="min-h-screen flex items-center justify-center main-bg p-4 text-center">
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="bg-card p-8 rounded-2xl shadow-2xl max-w-md w-full"
        >
          <ShieldOff className="mx-auto h-20 w-20 text-destructive mb-6" />
          <h1 className="text-4xl font-bold text-destructive mb-4">Access Denied</h1>
          <p className="text-muted-foreground text-lg">
            Your access to this service has been restricted.
          </p>
          <p className="text-muted-foreground mt-2">
            If you believe this is a mistake, please contact support.
          </p>
        </motion.div>
      </div>
    </>
  );
};

export default Banned;