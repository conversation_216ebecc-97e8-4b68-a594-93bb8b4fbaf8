import React from 'react';
import { Helmet } from 'react-helmet';
import PageWrapper from '@/components/PageWrapper';
import { Card, CardContent } from '@/components/ui/card';

const Disclaimer = () => {
  return (
    <>
      <Helmet>
        <title>Disclaimer - 6FootScripts</title>
        <meta name="description" content="Read the disclaimer for the 6FootScripts website and services." />
      </Helmet>
      <PageWrapper
        title="Disclaimer"
        description={`Last updated: ${new Date().toLocaleDateString()}`}
      >
        <Card>
          <CardContent className="pt-6 space-y-6">
            <div className="space-y-2">
              <h2 className="text-2xl font-semibold">General Information</h2>
              <p className="text-muted-foreground">The information and scripts provided by 6FootScripts are for general informational and educational purposes only. All information on the site is provided in good faith, however, we make no representation or warranty of any kind, express or implied, regarding the accuracy, adequacy, validity, reliability, availability, or completeness of any information or scripts on the site.</p>
            </div>

            <div className="space-y-2">
              <h2 className="text-2xl font-semibold">External Links Disclaimer</h2>
              <p className="text-muted-foreground">The site may contain links to other websites or content belonging to or originating from third parties. Such external links are not investigated, monitored, or checked for accuracy, adequacy, validity, reliability, availability, or completeness by us. We do not warrant, endorse, guarantee, or assume responsibility for the accuracy or reliability of any information offered by third-party websites linked through the site.</p>
            </div>

            <div className="space-y-2">
              <h2 className="text-2xl font-semibold">Scripts Disclaimer</h2>
              <p className="text-muted-foreground">The use of scripts can violate the terms of service of the games they are used in and may result in penalties, including but not limited to account suspension or termination. You assume full responsibility for any risks associated with using the scripts provided on this website. 6FootScripts and its developers are not liable for any consequences that may arise from the use of our scripts.</p>
            </div>
            
            <div className="space-y-2">
              <h2 className="text-2xl font-semibold">No Professional Advice</h2>
              <p className="text-muted-foreground">The information provided is not a substitute for professional advice. All content is for personal and educational use only. Accordingly, before taking any actions based upon such information, we encourage you to consult with the appropriate professionals. The use or reliance of any information or scripts contained on this site is solely at your own risk.</p>
            </div>
          </CardContent>
        </Card>
      </PageWrapper>
    </>
  );
};

export default Disclaimer;