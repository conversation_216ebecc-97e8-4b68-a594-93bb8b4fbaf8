import React from 'react';
import { Helmet } from 'react-helmet';
import PageWrapper from '@/components/PageWrapper';
import { Card, CardContent } from '@/components/ui/card';

const Privacy = () => {
  return (
    <>
      <Helmet>
        <title>Privacy Policy - 6FootScripts</title>
        <meta name="description" content="Read the Privacy Policy for the 6FootScripts website and services." />
      </Helmet>
      <PageWrapper
        title="Privacy Policy"
        description={`Last updated: ${new Date().toLocaleDateString()}`}
      >
        <Card>
          <CardContent className="pt-6 space-y-6">
            <div className="space-y-2">
              <h2 className="text-2xl font-semibold">1. Information We Collect</h2>
              <p className="text-muted-foreground">We may collect personal information that you voluntarily provide to us when you use our contact forms or interact with our website. This may include your name, email address, and any message you send.</p>
              <p className="text-muted-foreground">We may also collect non-personal information, such as browser type, operating system, and website usage data through analytics tools to improve our service.</p>
            </div>

            <div className="space-y-2">
              <h2 className="text-2xl font-semibold">2. How We Use Your Information</h2>
              <p className="text-muted-foreground">We use the information we collect to:</p>
              <ul className="list-disc list-inside text-muted-foreground space-y-1">
                <li>Respond to your inquiries and provide support.</li>
                <li>Improve our website and services.</li>
                <li>Monitor and analyze usage and trends.</li>
              </ul>
            </div>

            <div className="space-y-2">
              <h2 className="text-2xl font-semibold">3. Information Sharing</h2>
              <p className="text-muted-foreground">We do not sell, trade, or otherwise transfer your personally identifiable information to outside parties. This does not include trusted third parties who assist us in operating our website, so long as those parties agree to keep this information confidential.</p>
            </div>

            <div className="space-y-2">
              <h2 className="text-2xl font-semibold">4. Cookies</h2>
              <p className="text-muted-foreground">Our website may use "cookies" to enhance user experience. Your web browser places cookies on your hard drive for record-keeping purposes and sometimes to track information about them. You may choose to set your web browser to refuse cookies, or to alert you when cookies are being sent.</p>
            </div>

            <div className="space-y-2">
              <h2 className="text-2xl font-semibold">5. Security</h2>
              <p className="text-muted-foreground">We take reasonable measures to protect your personal information from unauthorized access, use, or disclosure. However, no internet-based site can be 100% secure, so we cannot guarantee absolute security.</p>
            </div>

            <div className="space-y-2">
              <h2 className="text-2xl font-semibold">6. Changes to This Policy</h2>
              <p className="text-muted-foreground">We may update this Privacy Policy from time to time. We will notify you of any changes by posting the new Privacy Policy on this page. You are advised to review this Privacy Policy periodically for any changes.</p>
            </div>

            <div className="space-y-2">
              <h2 className="text-2xl font-semibold">7. Contact Us</h2>
              <p className="text-muted-foreground">If you have any questions about this Privacy Policy, please contact us.</p>
            </div>
          </CardContent>
        </Card>
      </PageWrapper>
    </>
  );
};

export default Privacy;