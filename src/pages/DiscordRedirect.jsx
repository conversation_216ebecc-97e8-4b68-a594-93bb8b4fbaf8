import React, { useEffect } from 'react';
import { Helmet } from 'react-helmet';
import { motion } from 'framer-motion';
import { Disc } from 'lucide-react';

const DiscordRedirect = () => {
  const discordInviteUrl = 'https://discord.com/invite/6footscripts';

  useEffect(() => {
    const timer = setTimeout(() => {
      window.location.href = discordInviteUrl;
    }, 3000);

    return () => clearTimeout(timer);
  }, []);

  const devImages = [
    'https://storage.googleapis.com/hostinger-horizons-assets-prod/b372f44b-c5a5-4e91-95b3-5f8a56c2e92a/98f0f8020b8a290f454abc12077ff510.png',
    'https://storage.googleapis.com/hostinger-horizons-assets-prod/b372f44b-c5a5-4e91-95b3-5f8a56c2e92a/b37f650846d4e2af8503a5017ed63c94.png'
  ];

  return (
    <>
      <Helmet>
        <title>Redirecting to Discord - 6FootScripts</title>
        <meta name="description" content="Join our Discord community!" />
      </Helmet>
      <div className="min-h-screen flex flex-col items-center justify-center text-center p-4 main-bg">
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="flex flex-col items-center gap-6"
        >
          <div className="flex items-center gap-8">
            <motion.img
              src={devImages[0]}
              alt="6Foot4Honda avatar"
              className="w-24 h-24 md:w-32 md:h-32 rounded-full object-cover"
              initial={{ x: -100, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              transition={{ duration: 0.8, ease: 'easeOut', delay: 0.2 }}
            />
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ duration: 0.5, delay: 1 }}
            >
              <Disc className="w-16 h-16 text-primary animate-spin" style={{ animationDuration: '3s' }}/>
            </motion.div>
            <motion.img
              src={devImages[1]}
              alt="Lunarbine avatar"
              className="w-24 h-24 md:w-32 md:h-32 rounded-full object-cover"
              initial={{ x: 100, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              transition={{ duration: 0.8, ease: 'easeOut', delay: 0.2 }}
            />
          </div>
          <h1 className="text-3xl md:text-5xl font-bold mt-8">Joining our Discord...</h1>
          <p className="text-muted-foreground text-lg">You are being redirected. Hang tight!</p>
          <div className="mt-4 w-full max-w-md bg-muted rounded-full h-2.5 overflow-hidden">
            <motion.div
              className="bg-primary h-2.5 rounded-full"
              initial={{ x: "-100%" }}
              animate={{ x: "0%" }}
              transition={{ duration: 3, ease: "linear" }}
            />
          </div>
        </motion.div>
      </div>
    </>
  );
};

export default DiscordRedirect;