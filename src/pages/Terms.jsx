import React from 'react';
import { Helmet } from 'react-helmet';
import PageWrapper from '@/components/PageWrapper';
import { Card, CardContent } from '@/components/ui/card';

const Terms = () => {
  return (
    <>
      <Helmet>
        <title>Terms and Conditions - 6FootScripts</title>
        <meta name="description" content="Read the Terms and Conditions for using the 6FootScripts website and services." />
      </Helmet>
      <PageWrapper
        title="Terms and Conditions"
        description={`Last updated: ${new Date().toLocaleDateString()}`}
      >
        <Card>
          <CardContent className="pt-6 space-y-6">
            <div className="space-y-2">
              <h2 className="text-2xl font-semibold">1. Introduction</h2>
              <p className="text-muted-foreground">Welcome to 6FootScripts ("we", "our", "us"). These Terms and Conditions govern your use of our website and the scripts provided herein. By accessing or using our service, you agree to be bound by these terms.</p>
            </div>

            <div className="space-y-2">
              <h2 className="text-2xl font-semibold">2. Use of Scripts</h2>
              <p className="text-muted-foreground">Our scripts are provided for educational and personal use only. You agree to use them responsibly and in compliance with the terms of service of any game you are playing. We are not responsible for any actions taken against your game accounts as a result of using our scripts.</p>
            </div>

            <div className="space-y-2">
              <h2 className="text-2xl font-semibold">3. Intellectual Property</h2>
              <p className="text-muted-foreground">The content on this website, including text, graphics, logos, and the scripts themselves, are the property of 6FootScripts and its developers, and are protected by copyright and other intellectual property laws.</p>
            </div>

            <div className="space-y-2">
              <h2 className="text-2xl font-semibold">4. Disclaimer of Warranties</h2>
              <p className="text-muted-foreground">Our services and scripts are provided "as is" without any warranties, express or implied. We do not guarantee that the scripts will be error-free, uninterrupted, or meet your specific requirements.</p>
            </div>

            <div className="space-y-2">
              <h2 className="text-2xl font-semibold">5. Limitation of Liability</h2>
              <p className="text-muted-foreground">In no event shall 6FootScripts or its developers be liable for any direct, indirect, incidental, special, or consequential damages arising out of the use or inability to use our scripts or website.</p>
            </div>

            <div className="space-y-2">
              <h2 className="text-2xl font-semibold">6. Changes to Terms</h2>
              <p className="text-muted-foreground">We reserve the right to modify these terms at any time. We will notify users of any changes by posting the new terms on this page. Your continued use of the service after any such changes constitutes your acceptance of the new terms.</p>
            </div>

            <div className="space-y-2">
              <h2 className="text-2xl font-semibold">7. Contact Us</h2>
              <p className="text-muted-foreground">If you have any questions about these Terms, please contact us through our <a href="/contact" className="text-primary hover:underline">contact page</a>.</p>
            </div>
          </CardContent>
        </Card>
      </PageWrapper>
    </>
  );
};

export default Terms;