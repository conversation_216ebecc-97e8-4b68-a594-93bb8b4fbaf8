import React from 'react';
import { Helmet } from 'react-helmet';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Frown, FolderHeart as HomeIcon } from 'lucide-react';

const NotFound = () => {
  return (
    <>
      <Helmet>
        <title>404 Not Found - 6FootScripts</title>
        <meta name="description" content="Oops! The page you were looking for could not be found." />
      </Helmet>
      <div className="min-h-screen flex items-center justify-center main-bg text-foreground px-4">
        <motion.div
          initial={{ opacity: 0, y: -50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="text-center"
        >
          <div className="relative inline-block">
            <motion.div
              animate={{ y: [0, -15, 0] }}
              transition={{ duration: 1.5, repeat: Infinity, ease: "easeInOut" }}
            >
              <Frown className="w-32 h-32 text-primary mx-auto" strokeWidth={1.5}/>
            </motion.div>
            <p className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 text-8xl font-black text-foreground/10 -z-10">404</p>
          </div>
          <h1 className="mt-8 text-4xl md:text-6xl font-bold tracking-tighter">Page Not Found</h1>
          <p className="mt-4 text-lg text-muted-foreground">
            It seems you've wandered off the beaten path. Let's get you back home.
          </p>
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            className="mt-10"
          >
            <Button asChild size="lg">
              <Link to="/">
                <HomeIcon className="w-5 h-5 mr-2" />
                Go Back Home
              </Link>
            </Button>
          </motion.div>
        </motion.div>
      </div>
    </>
  );
};

export default NotFound;