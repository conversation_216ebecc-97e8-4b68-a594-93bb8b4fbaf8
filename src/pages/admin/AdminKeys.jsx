
import React, { useState, useEffect, useCallback } from 'react';
import { Helmet } from 'react-helmet';
import PageWrapper from '@/components/PageWrapper';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Loader2, KeyRound, Copy, Trash2, RotateCcw } from 'lucide-react';
import { motion } from 'framer-motion';
import { supabase } from '@/lib/supabaseClient';
import { useToast } from '@/components/ui/use-toast';
import { v4 as uuidv4 } from 'uuid';
import { useAdminAuth } from '@/contexts/AdminAuthContext';
import { apiClient } from '@/lib/apiClient';
import { getHwid } from '@/lib/hwid';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"

const AdminKeys = () => {
    const [keys, setKeys] = useState([]);
    const [newKey, setNewKey] = useState('');
    const [newKeyName, setNewKeyName] = useState('');
    const [loadingKeys, setLoadingKeys] = useState(true);
    const [isGenerating, setIsGenerating] = useState(false);
    const { toast } = useToast();
    const { getAuthHeaders } = useAdminAuth();

    const fetchKeys = useCallback(async () => {
        setLoadingKeys(true);
        try {
            const hwid = getHwid();
            const result = await apiClient.getAdminKeys(hwid);
            setKeys(result.data || []);
        } catch (error) {
            console.error('Error fetching keys:', error);
            toast({ variant: 'destructive', title: 'Error', description: 'Failed to fetch admin keys.' });
        } finally {
            setLoadingKeys(false);
        }
    }, [toast]);

    useEffect(() => {
        fetchKeys();
    }, [fetchKeys]);

    const generateNewKey = async () => {
        if (!newKeyName) {
            toast({ title: 'Name Required', description: 'Please provide a name for the key holder.', variant: 'destructive' });
            return;
        }

        setIsGenerating(true);
        setNewKey('');
        const generatedKey = uuidv4().toUpperCase();
        const data = await apiCall('create_key', { key: generatedKey, name: newKeyName });

        if (data) {
            setNewKey(data.access_key);
            toast({ title: 'Success!', description: 'New key generated and ready to be shared.' });
            setNewKeyName('');
            fetchKeys();
        }
        setIsGenerating(false);
    };
    
    const copyToClipboard = (text) => {
        navigator.clipboard.writeText(text);
        toast({ title: 'Copied!', description: 'Key copied to clipboard.' });
    };

    const unassignKey = async (keyId) => {
        const data = await apiCall('unassign_key', { id: keyId });
        if (data !== null) {
            toast({ title: 'Success!', description: 'Key has been unassigned.' });
            fetchKeys();
        }
    };

    const deleteKey = async (keyId) => {
        const data = await apiCall('delete_key', { id: keyId });
        if (data !== null) {
            toast({ title: 'Success!', description: 'Key has been deleted.' });
            fetchKeys();
        }
    };

    return (
        <>
            <Helmet>
                <title>Key Manager - 6FootScripts Admin</title>
            </Helmet>
            <PageWrapper title="Access Key Manager" description="Generate, manage, and revoke admin access keys.">
                <Card>
                    <CardHeader>
                        <CardTitle>Generate Key</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                        <div className="space-y-2">
                           <Label htmlFor="key-name">Admin Name</Label>
                           <Input id="key-name" placeholder="Enter name for the new key" value={newKeyName} onChange={e => setNewKeyName(e.target.value)} />
                        </div>
                        <Button onClick={generateNewKey} disabled={isGenerating}>
                            {isGenerating ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <KeyRound className="mr-2 h-4 w-4" />}
                            Generate New Key
                        </Button>
                        {newKey && (
                            <motion.div initial={{ opacity: 0, y: -10 }} animate={{ opacity: 1, y: 0 }}>
                                <Alert className="mt-4">
                                    <KeyRound className="h-4 w-4" />
                                    <AlertTitle>New Key Generated!</AlertTitle>
                                    <AlertDescription>Share this key with a new admin. It can only be used once to bind to a device.</AlertDescription>
                                    <div className="flex w-full items-center space-x-2 mt-2">
                                        <Input readOnly value={newKey} />
                                        <Button variant="outline" size="icon" onClick={() => copyToClipboard(newKey)}>
                                            <Copy className="h-4 w-4" />
                                        </Button>
                                    </div>
                                </Alert>
                            </motion.div>
                        )}
                    </CardContent>
                </Card>

                <Card className="mt-8">
                    <CardHeader>
                        <CardTitle>Existing Keys</CardTitle>
                        <CardDescription>View and manage all generated access keys.</CardDescription>
                    </CardHeader>
                    <CardContent>
                        {loadingKeys ? <div className="flex justify-center py-8"><Loader2 className="animate-spin h-8 w-8 text-primary" /></div> : (
                            <div className="rounded-md border">
                                <div className="max-h-[50vh] overflow-y-auto">
                                    {keys.map(key => (
                                        <div key={key.id} className="flex justify-between items-center p-3 border-b last:border-b-0">
                                            <div className="flex-1 min-w-0">
                                                <p className="font-semibold">{key.name}</p>
                                                <p className="font-mono text-xs text-muted-foreground truncate" title={key.access_key}>{key.access_key}</p>
                                                <p className="text-xs text-muted-foreground truncate" title={key.hwid || 'Not assigned'}>
                                                    HWID: {key.hwid || 'N/A'}
                                                </p>
                                            </div>
                                            <div className="flex items-center gap-2 ml-4">
                                                {key.hwid ? 
                                                    <AlertDialog>
                                                        <AlertDialogTrigger asChild>
                                                            <Button variant="outline" size="sm"><RotateCcw className="w-4 h-4 mr-2"/>Unassign</Button>
                                                        </AlertDialogTrigger>
                                                        <AlertDialogContent>
                                                            <AlertDialogHeader><AlertDialogTitle>Are you sure?</AlertDialogTitle><AlertDialogDescription>This will allow the key to be assigned to a new device.</AlertDialogDescription></AlertDialogHeader>
                                                            <AlertDialogFooter><AlertDialogCancel>Cancel</AlertDialogCancel><AlertDialogAction onClick={() => unassignKey(key.id)}>Unassign</AlertDialogAction></AlertDialogFooter>
                                                        </AlertDialogContent>
                                                    </AlertDialog>
                                                    : <span className="text-xs text-yellow-500 font-semibold px-2">Unassigned</span>
                                                }
                                                <AlertDialog>
                                                    <AlertDialogTrigger asChild>
                                                        <Button variant="destructive" size="icon"><Trash2 className="w-4 h-4"/></Button>
                                                    </AlertDialogTrigger>
                                                    <AlertDialogContent>
                                                        <AlertDialogHeader><AlertDialogTitle>Delete this key?</AlertDialogTitle><AlertDialogDescription>This action cannot be undone. The key will be permanently deleted.</AlertDialogDescription></AlertDialogHeader>
                                                        <AlertDialogFooter><AlertDialogCancel>Cancel</AlertDialogCancel><AlertDialogAction className="bg-destructive hover:bg-destructive/90" onClick={() => deleteKey(key.id)}>Delete</AlertDialogAction></AlertDialogFooter>
                                                    </AlertDialogContent>
                                                </AlertDialog>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        )}
                    </CardContent>
                </Card>
            </PageWrapper>
        </>
    )
}

export default AdminKeys;
