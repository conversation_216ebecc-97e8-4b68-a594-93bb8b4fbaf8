
import React from 'react';
import { NavLink, Link, useNavigate } from 'react-router-dom';
import { LayoutDashboard, KeyRound, FileText, Code, Users, LogOut, Code2, UserCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import { useAdminAuth } from '@/contexts/AdminAuthContext';

const navItems = [
  { to: '/admin/main_panel', icon: LayoutDashboard, label: 'Main Panel' },
  { to: '/admin/keys', icon: KeyRound, label: 'Keys' },
  { to: '/admin/requests', icon: FileText, label: 'Requests' },
  { to: '/admin/scripts', icon: Code, label: 'Scripts' },
  { to: '/admin/users', icon: Users, label: 'Users' },
];

const AdminHeader = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const { adminName } = useAdminAuth();

  const handleLogout = () => {
    sessionStorage.removeItem('6footscripts_admin_auth');
    sessionStorage.removeItem('6footscripts_admin_name');
    sessionStorage.removeItem('6footscripts_admin_key');
    toast({ title: 'Logged Out', description: 'You have been successfully logged out.' });
    navigate('/admin/login');
  };

  return (
    <header className="bg-card border-b flex items-center justify-between px-6 h-20 flex-shrink-0">
      <div className="flex items-center gap-8">
        <Link to="/" className="flex items-center gap-2">
          <Code2 className="w-8 h-8 text-primary" />
          <span className="text-xl font-bold">6FootAdmin</span>
        </Link>
        <nav className="hidden md:flex items-center gap-2">
          {navItems.map(item => (
            <NavLink
              key={item.to}
              to={item.to}
              className={({ isActive }) =>
                `flex items-center gap-2 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                  isActive
                    ? 'bg-primary text-primary-foreground'
                    : 'text-muted-foreground hover:bg-accent hover:text-accent-foreground'
                }`
              }
            >
              <item.icon className="w-4 h-4" />
              <span>{item.label}</span>
            </NavLink>
          ))}
        </nav>
      </div>
      <div className="flex items-center gap-4">
        <div className="flex items-center gap-3">
          <UserCircle className="w-8 h-8 text-muted-foreground" />
          <div>
            <p className="text-sm font-semibold">{adminName || 'Administrator'}</p>
            <p className="text-xs text-muted-foreground">Online</p>
          </div>
        </div>
        <Button variant="ghost" size="icon" onClick={handleLogout}>
          <LogOut className="w-5 h-5" />
        </Button>
      </div>
    </header>
  );
};

export default AdminHeader;
